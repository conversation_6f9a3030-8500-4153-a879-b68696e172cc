<template>
  <div class="game-rows-container">
    <!-- No Games State -->
    <div v-if="!games.length" class="text-center q-pa-xl text-grey-6">
      <q-icon name="games" size="4em" class="q-mb-md" />
      <div class="text-h6">No games available</div>
      <div class="text-body2">Check back later for new games</div>
    </div>

    <!-- Games List -->
    <div v-else class="games-list">
      <div
        v-for="game in games"
        :key="game.uuid"
        class="game-row"
        :class="{ 'game-row-hidden': game.is_hidden_from_landing_page }"
      >
        <div 
          class="game-row-content"
          :style="getBackgroundStyle(game)"
        >
          <!-- Background overlay for better text readability -->
          <div class="game-overlay">
            <div class="game-content-wrapper">
              <!-- Game Header -->
              <div class="game-header">
                <div class="game-title-section">
                  <h2 class="game-title">
                    {{ game.game_title || 'Untitled Game' }}
                  </h2>
                  <div class="game-badges">
                    <q-chip 
                      v-if="game.is_hidden_from_landing_page"
                      size="sm"
                      color="grey-7"
                      text-color="white"
                      icon="visibility_off"
                    >
                      Hidden
                    </q-chip>
                    <q-chip 
                      size="sm"
                      color="primary"
                      text-color="white"
                      icon="schedule"
                    >
                      {{ getGameStatus(game) }}
                    </q-chip>
                  </div>
                </div>
                
                <!-- Call to Action Button -->
                <div class="game-cta">
                  <q-btn
                    size="lg"
                    color="primary"
                    icon="play_arrow"
                    label="Play Now"
                    class="cta-button"
                    :to="getGameRoute(game)"
                    no-caps
                  />
                </div>
              </div>

              <!-- Game Description -->
              <div class="game-description">
                <p class="description-text">
                  {{ game.game_description || 'Test your property knowledge with this exciting game!' }}
                </p>
              </div>

              <!-- Game Stats -->
              <div class="game-stats">
                <div class="stats-row">
                  <div class="stat-item">
                    <q-icon name="home" size="sm" class="stat-icon" />
                    <span class="stat-value">{{ game.game_listings_count || 0 }}</span>
                    <span class="stat-label">Properties</span>
                  </div>
                  
                  <div class="stat-item">
                    <q-icon name="people" size="sm" class="stat-icon" />
                    <span class="stat-value">{{ game.game_sessions_count || 0 }}</span>
                    <span class="stat-label">Players</span>
                  </div>
                  
                  <div class="stat-item">
                    <q-icon name="quiz" size="sm" class="stat-icon" />
                    <span class="stat-value">{{ game.guessed_prices_count || 0 }}</span>
                    <span class="stat-label">Guesses</span>
                  </div>
                  
                  <div class="stat-item">
                    <q-icon name="attach_money" size="sm" class="stat-icon" />
                    <span class="stat-value">{{ game.game_default_currency || 'GBP' }}</span>
                    <span class="stat-label">Currency</span>
                  </div>
                </div>
              </div>

              <!-- Game Meta Info -->
              <div class="game-meta">
                <div class="meta-item">
                  <q-icon name="location_on" size="xs" />
                  <span>{{ game.game_default_country || 'UK' }}</span>
                </div>
                <div class="meta-item">
                  <q-icon name="event" size="xs" />
                  <span>{{ formatDateRange(game.game_start_at, game.game_end_at) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  games: {
    type: Array,
    default: () => []
  }
})

// Methods
const getBackgroundStyle = (game) => {
  const baseStyle = {
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat'
  }
  
  if (game.game_bg_image_url) {
    return {
      ...baseStyle,
      backgroundImage: `url(${game.game_bg_image_url})`
    }
  }
  
  // Fallback gradient based on game title or default
  const gradients = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
  ]
  
  const index = game.uuid ? game.uuid.charCodeAt(0) % gradients.length : 0
  
  return {
    ...baseStyle,
    background: gradients[index]
  }
}

const getGameRoute = (game) => {
  return {
    name: 'rPriceGameStart',
    params: { gameSlug: game.realty_game_slug }
  }
}

const getGameStatus = (game) => {
  const now = new Date()
  const startDate = game.game_start_at ? new Date(game.game_start_at) : null
  const endDate = game.game_end_at ? new Date(game.game_end_at) : null
  
  if (startDate && now < startDate) {
    return 'Coming Soon'
  } else if (endDate && now > endDate) {
    return 'Ended'
  } else {
    return 'Active'
  }
}

const formatDateRange = (startDate, endDate) => {
  if (!startDate && !endDate) return 'No dates set'
  
  const formatDate = (dateString) => {
    if (!dateString) return null
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      })
    } catch {
      return null
    }
  }
  
  const start = formatDate(startDate)
  const end = formatDate(endDate)
  
  if (start && end) {
    return `${start} - ${end}`
  } else if (start) {
    return `From ${start}`
  } else if (end) {
    return `Until ${end}`
  }
  
  return 'Ongoing'
}
</script>

<style scoped>
.game-rows-container {
  width: 100%;
}

.games-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.game-row {
  width: 100%;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.game-row:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.game-row-hidden {
  opacity: 0.7;
}

.game-row-content {
  position: relative;
  min-height: 300px;
  width: 100%;
}

.game-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  align-items: center;
  padding: 32px;
}

.game-content-wrapper {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.game-title-section {
  flex: 1;
}

.game-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin: 0 0 12px 0;
  line-height: 1.2;
}

.game-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.game-cta {
  flex-shrink: 0;
  margin-left: 24px;
}

.cta-button {
  font-size: 1.1rem;
  padding: 12px 32px;
  border-radius: 50px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.game-description {
  margin-bottom: 24px;
}

.description-text {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  max-width: 800px;
}

.game-stats {
  margin-bottom: 16px;
}

.stats-row {
  display: flex;
  gap: 32px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.stat-icon {
  color: rgba(255, 255, 255, 0.8);
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 600;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.game-meta {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-overlay {
    padding: 20px;
  }
  
  .game-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .game-cta {
    margin-left: 0;
    align-self: stretch;
  }
  
  .cta-button {
    width: 100%;
    justify-content: center;
  }
  
  .game-title {
    font-size: 2rem;
  }
  
  .description-text {
    font-size: 1.1rem;
  }
  
  .stats-row {
    gap: 16px;
  }
  
  .game-meta {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .game-overlay {
    padding: 16px;
  }
  
  .game-title {
    font-size: 1.8rem;
  }
  
  .description-text {
    font-size: 1rem;
  }
  
  .stats-row {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
